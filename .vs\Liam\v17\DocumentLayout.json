{"Version": 1, "WorkspaceRootPath": "D:\\Project\\00 Liam\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{C5BB863B-B263-4ADD-96D7-9D7298E7CC28}|tests\\Liam.Cryptography.Tests\\Liam.Cryptography.Tests.csproj|d:\\project\\00 liam\\tests\\liam.cryptography.tests\\extensions\\bytearraycryptoextensionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C5BB863B-B263-4ADD-96D7-9D7298E7CC28}|tests\\Liam.Cryptography.Tests\\Liam.Cryptography.Tests.csproj|solutionrelative:tests\\liam.cryptography.tests\\extensions\\bytearraycryptoextensionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "ByteArrayCryptoExtensionsTests.cs", "DocumentMoniker": "D:\\Project\\00 Liam\\tests\\Liam.Cryptography.Tests\\Extensions\\ByteArrayCryptoExtensionsTests.cs", "RelativeDocumentMoniker": "tests\\Liam.Cryptography.Tests\\Extensions\\ByteArrayCryptoExtensionsTests.cs", "ToolTip": "D:\\Project\\00 Liam\\tests\\Liam.Cryptography.Tests\\Extensions\\ByteArrayCryptoExtensionsTests.cs", "RelativeToolTip": "tests\\Liam.Cryptography.Tests\\Extensions\\ByteArrayCryptoExtensionsTests.cs", "ViewState": "AgIAAKQAAAAAAAAAAAAAwLUAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-15T01:26:55.535Z", "EditorCaption": ""}]}]}]}