//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: System.Reflection.AssemblyCompanyAttribute("Liam")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyDescriptionAttribute("Liam系列加密功能库，提供完整的对称加密、非对称加密、哈希算法、数字签名和密钥管理功能。支持AES、ChaCha20-Poly1305、RSA、ECDSA、SH" +
    "A256、Argon2等现代加密算法，包含流式处理和性能优化。")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.1.0")]
[assembly: System.Reflection.AssemblyProductAttribute("Liam.Cryptography")]
[assembly: System.Reflection.AssemblyTitleAttribute("Liam.Cryptography")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyMetadataAttribute("RepositoryUrl", "https://gitee.com/your-username/Liam")]

// 由 MSBuild WriteCodeFragment 类生成。

