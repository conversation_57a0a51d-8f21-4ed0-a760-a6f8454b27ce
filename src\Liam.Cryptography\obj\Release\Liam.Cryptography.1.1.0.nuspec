﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Liam.Cryptography</id>
    <version>1.1.0</version>
    <authors>Liam</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>icon.png</icon>
    <readme>README.md</readme>
    <description>Liam系列加密功能库，提供完整的对称加密、非对称加密、哈希算法、数字签名和密钥管理功能。支持AES、ChaCha20-Poly1305、RSA、ECDSA、SHA256、Argon2等现代加密算法，包含流式处理和性能优化。</description>
    <tags>cryptography encryption decryption aes chacha20 poly1305 rsa ecdsa ed25519 sha256 argon2 digital-signature key-management streaming performance liam</tags>
    <repository type="git" url="https://gitee.com/your-username/Liam" />
    <dependencies>
      <group targetFramework="net8.0">
        <dependency id="Konscious.Security.Cryptography.Argon2" version="1.3.1" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
  <files>
    <file src="D:\Project\00 Liam\src\Liam.Cryptography\bin\Release\net8.0\Liam.Cryptography.dll" target="lib\net8.0\Liam.Cryptography.dll" />
    <file src="D:\Project\00 Liam\src\Liam.Cryptography\bin\Release\net8.0\Liam.Cryptography.xml" target="lib\net8.0\Liam.Cryptography.xml" />
    <file src="D:\Project\00 Liam\src\Liam.Cryptography\README.md" target="\README.md" />
    <file src="D:\Project\00 Liam\icon.png" target="\icon.png" />
  </files>
</package>